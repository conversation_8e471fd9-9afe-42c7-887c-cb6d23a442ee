I want to write integration tests for test cases related to startup service functionality. My testing approach is:

1. **LocalStack**: Use TestContainers to spin up real AWS services for integration testing
2. **BCMonitoringApplication**: Use @SpringBootTest to ensure the application runs as close to production as possible
3. **Besu blockchain**: Mock the WebSocket connections (specifically the subscribe() and getFilterLogs() functions in MonitorEventService) to avoid actual blockchain calls

I have already implemented this approach in the file `/Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java/src/test/groovy/adhoc/startup/StartupServiceSpec.groovy`, but the tests are not working as expected.

Please:
1. Review my existing test code in the StartupServiceSpec.groovy file
2. Identify what's wrong with my current implementation
3. Fix any issues to make the integration tests work properly
4. Ensure the tests follow the testing preferences from our conversation history (proper mocking of WebSocket connections, stopping services during test execution, using correct default table names with hyphens)
5. Make sure the tests can run successfully with the described setup (TestContainers for LocalStack, SpringBootTest for the application, mocked Besu WebSocket)

The goal is to have working integration tests that validate the startup service behavior in a controlled test environment.